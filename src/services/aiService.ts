// AI服务类 - 负责AI功能的实际调用

import {
  AIConfig,
  AITagGenerationRequest,
  AITagGenerationResponse,
  AICategoryRequest,
  AICategoryResponse,
  AIDescriptionRequest,
  AIDescriptionResponse,
  AIServiceStats,
  AIServiceError,
  AIBatchRequest,
  AIBatchResponse
} from '../types/ai'
import { aiConfigService } from './aiConfigService'
import { aiCacheService } from './aiCacheService'
import { aiChatService } from './aiChatService'

/**
 * AI服务类
 * 提供标签生成、分类建议、描述生成等AI功能
 */
export class AIService {
  
  /**
   * 服务统计信息
   */
  private stats: AIServiceStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    errorRate: 0,
    tagGenerationCount: 0,
    categoryGenerationCount: 0,
    descriptionGenerationCount: 0,
    dailyStats: []
  }

  /**
   * 错误日志
   */
  private errorLog: AIServiceError[] = []

  /**
   * 批处理队列
   */
  private batchQueue: AIBatchRequest[] = []

  /**
   * 生成标签
   * @param request 标签生成请求
   * @param useCache 是否使用缓存
   * @returns Promise<AITagGenerationResponse>
   */
  async generateTags(request: AITagGenerationRequest, useCache: boolean = true): Promise<AITagGenerationResponse> {
    const startTime = Date.now()
    
    try {
      console.log('开始生成AI标签:', request.title || request.content?.substring(0, 50))
      
      // 使用aiChatService生成标签
      
      // 构建提示词
      const prompt = this.buildTagGenerationPrompt(request)
      
      const aiResponse = await aiChatService.generateText({
        prompt,
        generationType: 'tags',
        context: {
          title: request.title,
          content: request.content,
          url: request.url
        },
        maxLength: 200
      })
      
      // 解析响应
      const tags = this.parseTagsFromResponse(aiResponse.content)
      
      const processingTime = Date.now() - startTime
      
      // 更新统计
      this.updateStats('tags', true, processingTime)
      
      const response: AITagGenerationResponse = {
        tags,
        confidence: this.calculateConfidence(aiResponse.content),
        reasoning: this.extractReasoning(aiResponse.content),
        processingTime
      }

      // 保存到缓存
      if (useCache && response.confidence > 0.7) {
        await aiCacheService.saveTagsCache(request, response)
      }

      console.log(`AI标签生成成功: ${tags.join(', ')}`)
      return response

    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error('AI标签生成失败:', error)
      
      // 记录错误
      this.logError(error, 'generateTags', await aiConfigService.getConfig())
      
      // 更新统计
      this.updateStats('tags', false, processingTime)
      
      // 尝试降级策略
      return await this.fallbackTagGeneration(request)
    }
  }

  /**
   * 生成分类建议
   * @param request 分类建议请求
   * @param useCache 是否使用缓存
   * @returns Promise<AICategoryResponse>
   */
  async generateCategory(request: AICategoryRequest, useCache: boolean = true): Promise<AICategoryResponse> {
    const startTime = Date.now()

    try {
      console.log('开始生成AI分类建议:', request.title || request.content.substring(0, 50))

      // 检查缓存
      if (useCache) {
        const cachedResult = await aiCacheService.getCategoryCache(request)
        if (cachedResult) {
          console.log('使用缓存的AI分类结果')
          return cachedResult
        }
      }

      // 构建提示词
      const prompt = this.buildCategoryGenerationPrompt(request)

      // 使用aiChatService生成分类建议
      const aiResponse = await aiChatService.generateText({
        prompt,
        generationType: 'category',
        context: {
          title: request.title,
          content: request.content,
          url: request.url
        },
        maxLength: 100
      })

      // 解析响应
      const categoryData = this.parseCategoryFromResponse(aiResponse.content)

      const processingTime = Date.now() - startTime

      // 更新统计
      this.updateStats('category', true, processingTime)

      const response: AICategoryResponse = {
        category: categoryData.category,
        confidence: categoryData.confidence,
        alternatives: categoryData.alternatives,
        reasoning: this.extractReasoning(aiResponse.content)
      }

      // 保存到缓存
      if (useCache && response.confidence > 0.7) {
        await aiCacheService.saveCategoryCache(request, response)
      }

      console.log(`AI分类建议成功: ${categoryData.category}`)
      return response

    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error('AI分类建议失败:', error)

      // 记录错误
      this.logError(error, 'generateCategory', await aiConfigService.getConfig())

      // 更新统计
      this.updateStats('category', false, processingTime)

      // 尝试降级策略
      return await this.fallbackCategoryGeneration(request)
    }
  }

  /**
   * 生成描述
   * @param request 描述生成请求
   * @param useCache 是否使用缓存
   * @returns Promise<AIDescriptionResponse>
   */
  async generateDescription(request: AIDescriptionRequest, useCache: boolean = true): Promise<AIDescriptionResponse> {
    const startTime = Date.now()

    try {
      console.log('开始生成AI描述:', request.title || request.content.substring(0, 50))

      // 检查缓存
      if (useCache) {
        const cachedResult = await aiCacheService.getDescriptionCache(request)
        if (cachedResult) {
          console.log('使用缓存的AI描述结果')
          return cachedResult
        }
      }

      // 构建提示词
      const prompt = this.buildDescriptionGenerationPrompt(request)

      // 使用aiChatService生成描述
      const aiResponse = await aiChatService.generateText({
        prompt,
        generationType: 'description',
        context: {
          title: request.title,
          content: request.content,
          url: request.url
        },
        maxLength: 300
      })

      // 解析响应
      const description = this.parseDescriptionFromResponse(aiResponse.content)

      const processingTime = Date.now() - startTime

      // 更新统计
      this.updateStats('description', true, processingTime)

      const response: AIDescriptionResponse = {
        description,
        confidence: this.calculateConfidence(aiResponse.content),
        wordCount: description.split(/\s+/).length
      }

      // 保存到缓存
      if (useCache && response.confidence > 0.7) {
        await aiCacheService.saveDescriptionCache(request, response)
      }

      console.log(`AI描述生成成功: ${description.substring(0, 100)}...`)
      return response

    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error('AI描述生成失败:', error)
      
      // 记录错误
      this.logError(error, 'generateDescription', await aiConfigService.getConfig())
      
      // 更新统计
      this.updateStats('description', false, processingTime)
      
      // 尝试降级策略
      return await this.fallbackDescriptionGeneration(request)
    }
  }

  /**
   * 批量处理AI请求
   * @param batchRequest 批处理请求
   * @returns Promise<AIBatchResponse>
   */
  async processBatch(batchRequest: AIBatchRequest): Promise<AIBatchResponse> {
    const startTime = Date.now()
    
    try {
      console.log(`开始批量处理AI请求: ${batchRequest.items.length}个项目`)
      
      const results = []
      
      for (const item of batchRequest.items) {
        try {
          let result: any
          
          switch (item.type) {
            case 'tags':
              result = await this.generateTags(item.data as AITagGenerationRequest)
              break
            case 'category':
              result = await this.generateCategory(item.data as AICategoryRequest)
              break
            case 'description':
              result = await this.generateDescription(item.data as AIDescriptionRequest)
              break
            default:
              throw new Error(`不支持的批处理类型: ${item.type}`)
          }
          
          results.push({
            id: item.id,
            success: true,
            data: result
          })
          
        } catch (error) {
          results.push({
            id: item.id,
            success: false,
            error: (error as Error).message
          })
        }
        
        // 添加延迟以避免API限制
        await this.delay(100)
      }
      
      const totalProcessingTime = Date.now() - startTime
      
      const response: AIBatchResponse = {
        id: batchRequest.id,
        results,
        completedAt: new Date(),
        totalProcessingTime
      }

      console.log(`批量处理完成: ${results.filter(r => r.success).length}/${results.length} 成功`)
      return response

    } catch (error) {
      console.error('批量处理失败:', error)
      throw error
    }
  }

  /**
   * 获取服务统计信息
   * @returns AIServiceStats
   */
  getStats(): AIServiceStats {
    return { ...this.stats }
  }

  /**
   * 获取错误日志
   * @param limit 限制数量
   * @returns AIServiceError[]
   */
  getErrorLog(limit: number = 50): AIServiceError[] {
    return this.errorLog.slice(-limit)
  }

  /**
   * 清空错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
    console.log('AI服务错误日志已清空')
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      errorRate: 0,
      tagGenerationCount: 0,
      categoryGenerationCount: 0,
      descriptionGenerationCount: 0,
      dailyStats: []
    }
    console.log('AI服务统计信息已重置')
  }

  // ==================== 私有方法 ====================

  /**
   * 带重试机制的AI服务调用
   * @param config AI配置
   * @param prompt 提示词
   * @param operation 操作类型
   * @param maxRetries 最大重试次数
   * @returns Promise<string>
   */
  private async callAIServiceWithRetry(config: AIConfig, prompt: string, operation: string, maxRetries: number = 3): Promise<string> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`AI服务调用尝试 ${attempt}/${maxRetries} (${operation})`)
        
        const result = await this.callAIService(config, prompt, operation)
        
        if (attempt > 1) {
          console.log(`AI服务调用在第 ${attempt} 次尝试后成功`)
        }
        
        return result
      } catch (error) {
        lastError = error as Error
        console.warn(`AI服务调用第 ${attempt} 次尝试失败:`, (error as Error).message)
        
        // 如果不是最后一次尝试，等待后重试
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // 指数退避，最大10秒
          console.log(`等待 ${delay}ms 后重试...`)
          await this.delay(delay)
        }
      }
    }
    
    console.error(`AI服务调用在 ${maxRetries} 次尝试后仍然失败`)
    throw lastError || new Error('AI服务调用失败')
  }

  /**
   * 调用AI服务
   * @param config AI配置
   * @param prompt 提示词
   * @param operation 操作类型
   * @returns Promise<string>
   */
  private async callAIService(config: AIConfig, prompt: string, operation: string): Promise<string> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...config.customHeaders
      }

      // 根据不同提供商设置认证头
      if (config.apiKey) {
        switch (config.provider) {
          case 'openai':
          case 'custom':
            headers['Authorization'] = `Bearer ${config.apiKey}`
            break
          case 'claude':
            headers['x-api-key'] = config.apiKey
            headers['anthropic-version'] = '2023-06-01'
            break
          case 'gemini':
            // Gemini使用URL参数传递API密钥
            break
        }
      }

      // 构建请求体
      const requestBody = this.buildRequestBody(config, prompt)
      
      // 构建URL
      const url = this.buildRequestUrl(config)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(config.timeout || 30000)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      return this.extractResponseContent(data, config.provider)

    } catch (error) {
      console.error(`AI服务调用失败 (${operation}):`, error)
      throw error
    }
  }

  /**
   * 构建请求URL
   */
  private buildRequestUrl(config: AIConfig): string {
    let url = config.baseUrl

    switch (config.provider) {
      case 'openai':
      case 'custom':
      case 'local':
        url += '/chat/completions'
        break
      case 'claude':
        url += '/messages'
        break
      case 'gemini':
        url += `/models/${config.model}:generateContent?key=${config.apiKey}`
        break
    }

    return url
  }

  /**
   * 构建请求体
   */
  private buildRequestBody(config: AIConfig, prompt: string): any {
    const baseBody = {
      model: config.model || 'gpt-3.5-turbo',
      temperature: config.temperature || 0.7,
      max_tokens: config.maxTokens || 1000,
      ...config.customParams
    }

    switch (config.provider) {
      case 'openai':
      case 'custom':
      case 'local':
        return {
          ...baseBody,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        }

      case 'claude':
        return {
          ...baseBody,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        }

      case 'gemini':
        return {
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: config.temperature || 0.7,
            maxOutputTokens: config.maxTokens || 1000
          }
        }

      default:
        throw new Error(`不支持的提供商: ${config.provider}`)
    }
  }

  /**
   * 提取响应内容
   */
  private extractResponseContent(data: any, provider: string): string {
    switch (provider) {
      case 'openai':
      case 'custom':
      case 'local':
        return data.choices?.[0]?.message?.content || ''

      case 'claude':
        return data.content?.[0]?.text || ''

      case 'gemini':
        return data.candidates?.[0]?.content?.parts?.[0]?.text || ''

      default:
        throw new Error(`不支持的提供商: ${provider}`)
    }
  }

  /**
   * 构建标签生成提示词
   */
  private buildTagGenerationPrompt(request: AITagGenerationRequest): string {
    const { content, title, url, existingTags, maxTags = 5, language = 'zh-CN' } = request

    let prompt = `请为以下内容生成${maxTags}个相关的标签。标签应该简洁、准确，能够帮助用户快速理解和分类内容。\n\n`

    if (title) {
      prompt += `标题: ${title}\n`
    }

    if (url) {
      prompt += `网址: ${url}\n`
    }

    prompt += `内容: ${content.substring(0, 1000)}\n\n`

    if (existingTags && existingTags.length > 0) {
      prompt += `现有标签: ${existingTags.join(', ')}\n请避免重复现有标签。\n\n`
    }

    prompt += `要求:\n`
    prompt += `1. 生成${maxTags}个标签\n`
    prompt += `2. 标签应该简洁明了，通常1-3个字\n`
    prompt += `3. 标签应该具有代表性和实用性\n`
    prompt += `4. 使用${language === 'zh-CN' ? '中文' : '英文'}\n`
    prompt += `5. 只返回标签列表，用逗号分隔，不要其他解释\n\n`
    prompt += `标签:`

    return prompt
  }

  /**
   * 构建分类生成提示词
   */
  private buildCategoryGenerationPrompt(request: AICategoryRequest): string {
    const { content, title, url, existingCategories, maxSuggestions = 3 } = request

    let prompt = `请为以下内容推荐一个最合适的分类，并提供${maxSuggestions}个备选分类。\n\n`

    if (title) {
      prompt += `标题: ${title}\n`
    }

    if (url) {
      prompt += `网址: ${url}\n`
    }

    prompt += `内容: ${content.substring(0, 1000)}\n\n`

    if (existingCategories && existingCategories.length > 0) {
      prompt += `现有分类: ${existingCategories.join(', ')}\n`
      prompt += `请优先从现有分类中选择，如果都不合适再创建新分类。\n\n`
    }

    prompt += `要求:\n`
    prompt += `1. 推荐一个最合适的主分类\n`
    prompt += `2. 提供${maxSuggestions}个备选分类\n`
    prompt += `3. 分类名称应该简洁明了\n`
    prompt += `4. 使用中文\n`
    prompt += `5. 按以下格式返回:\n`
    prompt += `主分类: [分类名称]\n`
    prompt += `备选分类: [分类1], [分类2], [分类3]\n`
    prompt += `推荐理由: [简要说明]\n\n`
    prompt += `分类建议:`

    return prompt
  }

  /**
   * 构建描述生成提示词
   */
  private buildDescriptionGenerationPrompt(request: AIDescriptionRequest): string {
    const { content, title, url, maxLength = 200, style = 'brief' } = request

    let prompt = `请为以下内容生成一个${style === 'brief' ? '简洁' : style === 'detailed' ? '详细' : '摘要'}的描述。\n\n`

    if (title) {
      prompt += `标题: ${title}\n`
    }

    if (url) {
      prompt += `网址: ${url}\n`
    }

    prompt += `内容: ${content.substring(0, 2000)}\n\n`

    prompt += `要求:\n`
    prompt += `1. 描述长度不超过${maxLength}字\n`
    prompt += `2. 准确概括内容要点\n`
    prompt += `3. 语言简洁流畅\n`
    prompt += `4. 使用中文\n`
    prompt += `5. 只返回描述内容，不要其他解释\n\n`
    prompt += `描述:`

    return prompt
  }

  /**
   * 从响应中解析标签
   */
  private parseTagsFromResponse(response: string): string[] {
    try {
      // 清理响应文本
      const cleanResponse = response.trim().replace(/^标签:?\s*/, '')
      
      // 分割标签
      const tags = cleanResponse
        .split(/[,，、\n]/)
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0 && tag.length <= 20)
        .slice(0, 10) // 最多10个标签

      return tags.length > 0 ? tags : ['未分类']
    } catch (error) {
      console.error('解析标签失败:', error)
      return ['未分类']
    }
  }

  /**
   * 从响应中解析分类
   */
  private parseCategoryFromResponse(response: string): {
    category: string
    confidence: number
    alternatives?: Array<{category: string, confidence: number}>
  } {
    try {
      const lines = response.trim().split('\n')
      let category = '默认分类'
      let alternatives: Array<{category: string, confidence: number}> = []

      for (const line of lines) {
        if (line.includes('主分类:') || line.includes('分类:')) {
          category = line.split(':')[1]?.trim() || '默认分类'
        } else if (line.includes('备选分类:')) {
          const altText = line.split(':')[1]?.trim() || ''
          alternatives = altText
            .split(/[,，]/)
            .map(alt => alt.trim())
            .filter(alt => alt.length > 0)
            .map((alt, index) => ({
              category: alt,
              confidence: 0.8 - (index * 0.1)
            }))
        }
      }

      return {
        category,
        confidence: 0.9,
        alternatives
      }
    } catch (error) {
      console.error('解析分类失败:', error)
      return {
        category: '默认分类',
        confidence: 0.5
      }
    }
  }

  /**
   * 从响应中解析描述
   */
  private parseDescriptionFromResponse(response: string): string {
    try {
      // 清理响应文本
      const cleanResponse = response.trim().replace(/^描述:?\s*/, '')
      
      // 限制长度
      return cleanResponse.length > 500 
        ? cleanResponse.substring(0, 500) + '...'
        : cleanResponse
    } catch (error) {
      console.error('解析描述失败:', error)
      return '暂无描述'
    }
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(response: string): number {
    // 简单的置信度计算逻辑
    const length = response.length
    if (length > 100) return 0.9
    if (length > 50) return 0.8
    if (length > 20) return 0.7
    return 0.6
  }

  /**
   * 提取推理过程
   */
  private extractReasoning(response: string): string | undefined {
    const lines = response.split('\n')
    for (const line of lines) {
      if (line.includes('推荐理由:') || line.includes('理由:')) {
        return line.split(':')[1]?.trim()
      }
    }
    return undefined
  }

  /**
   * 更新统计信息
   */
  private updateStats(type: 'tags' | 'category' | 'description', success: boolean, responseTime: number): void {
    this.stats.totalRequests++
    
    if (success) {
      this.stats.successfulRequests++
    } else {
      this.stats.failedRequests++
    }

    // 更新平均响应时间
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests

    // 更新错误率
    this.stats.errorRate = this.stats.failedRequests / this.stats.totalRequests

    // 更新功能统计
    switch (type) {
      case 'tags':
        this.stats.tagGenerationCount++
        break
      case 'category':
        this.stats.categoryGenerationCount++
        break
      case 'description':
        this.stats.descriptionGenerationCount++
        break
    }

    // 更新最后请求时间
    this.stats.lastRequestDate = new Date()

    // 更新每日统计
    this.updateDailyStats(success)
  }

  /**
   * 更新每日统计
   */
  private updateDailyStats(success: boolean): void {
    const today = new Date().toISOString().split('T')[0]
    let todayStats = this.stats.dailyStats.find(stat => stat.date === today)

    if (!todayStats) {
      todayStats = { date: today, requests: 0, errors: 0 }
      this.stats.dailyStats.push(todayStats)
    }

    todayStats.requests++
    if (!success) {
      todayStats.errors++
    }

    // 只保留最近30天的统计
    this.stats.dailyStats = this.stats.dailyStats.slice(-30)
  }

  /**
   * 记录错误
   */
  private logError(error: any, operation: string, config: AIConfig): void {
    const aiError: AIServiceError = {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message,
      details: error.details,
      timestamp: new Date(),
      provider: config.provider,
      operation
    }

    this.errorLog.push(aiError)

    // 只保留最近100个错误
    this.errorLog = this.errorLog.slice(-100)
  }

  /**
   * 标签生成降级策略
   * @param request 标签生成请求
   * @returns Promise<AITagGenerationResponse>
   */
  private async fallbackTagGeneration(request: AITagGenerationRequest): Promise<AITagGenerationResponse> {
    console.log('使用标签生成降级策略')
    
    const fallbackTags: string[] = []
    
    // 基于URL域名生成标签
    if (request.url) {
      try {
        const domain = new URL(request.url).hostname
        const domainParts = domain.split('.')
        
        // 添加域名相关标签
        if (domainParts.includes('github')) {
          fallbackTags.push('开发', '代码')
        } else if (domainParts.includes('stackoverflow')) {
          fallbackTags.push('编程', '问答')
        } else if (domainParts.includes('youtube')) {
          fallbackTags.push('视频', '娱乐')
        } else if (domainParts.includes('wikipedia')) {
          fallbackTags.push('百科', '知识')
        } else if (domainParts.includes('news') || domainParts.includes('xinhua') || domainParts.includes('people')) {
          fallbackTags.push('新闻', '资讯')
        } else {
          fallbackTags.push('网站')
        }
      } catch (error) {
        console.warn('解析URL失败:', error)
      }
    }
    
    // 基于标题和内容的关键词提取
    const text = `${request.title || ''} ${request.content}`.toLowerCase()
    
    const keywordMap: Record<string, string[]> = {
      '技术': ['javascript', 'python', 'java', 'react', 'vue', 'node', 'api', '开发', '编程', '代码'],
      '学习': ['教程', '学习', '课程', '培训', '指南', '文档'],
      '工具': ['工具', 'tool', '软件', '应用', 'app'],
      '设计': ['设计', 'design', 'ui', 'ux', '界面'],
      '商业': ['商业', '营销', '市场', '销售', '创业'],
      '生活': ['生活', '健康', '美食', '旅游', '娱乐'],
      '科学': ['科学', '研究', '技术', '创新', '发现']
    }
    
    for (const [tag, keywords] of Object.entries(keywordMap)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        fallbackTags.push(tag)
      }
    }
    
    // 如果没有找到任何标签，使用默认标签
    if (fallbackTags.length === 0) {
      fallbackTags.push('收藏', '资源')
    }
    
    // 限制标签数量
    const maxTags = request.maxTags || 5
    const finalTags = fallbackTags.slice(0, maxTags)
    
    return {
      tags: finalTags,
      confidence: 0.6, // 降级策略的置信度较低
      reasoning: '使用本地规则生成标签（AI服务不可用）',
      processingTime: 50 // 本地处理很快
    }
  }

  /**
   * 分类生成降级策略
   * @param request 分类建议请求
   * @returns Promise<AICategoryResponse>
   */
  private async fallbackCategoryGeneration(request: AICategoryRequest): Promise<AICategoryResponse> {
    console.log('使用分类生成降级策略')
    
    let category = '默认分类'
    const alternatives: Array<{category: string, confidence: number}> = []
    
    // 基于URL域名判断分类
    if (request.url) {
      try {
        const domain = new URL(request.url).hostname
        
        if (domain.includes('github') || domain.includes('gitlab')) {
          category = '开发工具'
          alternatives.push(
            { category: '代码仓库', confidence: 0.8 },
            { category: '技术资源', confidence: 0.7 }
          )
        } else if (domain.includes('stackoverflow') || domain.includes('csdn')) {
          category = '技术问答'
          alternatives.push(
            { category: '编程学习', confidence: 0.8 },
            { category: '开发资源', confidence: 0.7 }
          )
        } else if (domain.includes('youtube') || domain.includes('bilibili')) {
          category = '视频资源'
          alternatives.push(
            { category: '娱乐视频', confidence: 0.8 },
            { category: '学习视频', confidence: 0.7 }
          )
        } else if (domain.includes('wikipedia') || domain.includes('baidu')) {
          category = '知识百科'
          alternatives.push(
            { category: '参考资料', confidence: 0.8 },
            { category: '学习资源', confidence: 0.7 }
          )
        }
      } catch (error) {
        console.warn('解析URL失败:', error)
      }
    }
    
    // 基于内容关键词判断分类
    const text = `${request.title || ''} ${request.content}`.toLowerCase()
    
    if (text.includes('教程') || text.includes('学习') || text.includes('课程')) {
      category = '学习资源'
    } else if (text.includes('工具') || text.includes('软件') || text.includes('应用')) {
      category = '工具软件'
    } else if (text.includes('新闻') || text.includes('资讯') || text.includes('报道')) {
      category = '新闻资讯'
    } else if (text.includes('设计') || text.includes('ui') || text.includes('界面')) {
      category = '设计资源'
    }
    
    return {
      category,
      confidence: 0.6,
      alternatives,
      reasoning: '使用本地规则生成分类（AI服务不可用）'
    }
  }

  /**
   * 描述生成降级策略
   * @param request 描述生成请求
   * @returns Promise<AIDescriptionResponse>
   */
  private async fallbackDescriptionGeneration(request: AIDescriptionRequest): Promise<AIDescriptionResponse> {
    console.log('使用描述生成降级策略')
    
    let description = ''
    
    // 使用标题作为基础描述
    if (request.title) {
      description = request.title
    }
    
    // 从内容中提取前几句话
    if (request.content) {
      const sentences = request.content
        .replace(/\n+/g, ' ')
        .split(/[。！？.!?]/)
        .filter(s => s.trim().length > 10)
        .slice(0, 3)
      
      if (sentences.length > 0) {
        const contentSummary = sentences.join('。') + '。'
        description = description ? `${description}。${contentSummary}` : contentSummary
      }
    }
    
    // 如果没有有效描述，使用默认描述
    if (!description.trim()) {
      description = '这是一个收藏的资源，暂无详细描述。'
    }
    
    // 限制长度
    const maxLength = request.maxLength || 200
    if (description.length > maxLength) {
      description = description.substring(0, maxLength - 3) + '...'
    }
    
    return {
      description,
      confidence: 0.5,
      wordCount: description.length
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 导出单例实例
export const aiService = new AIService()